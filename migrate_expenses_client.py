#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف ترحيل لإضافة عمود العميل إلى جدول المصروفات
Migration script to add client_id column to expenses table
"""

import sqlite3
import os
from datetime import datetime

def migrate_database():
    """تحديث قاعدة البيانات لإضافة ربط المصروفات بالعملاء"""
    
    # مسار قاعدة البيانات
    db_path = "smart_finish.db"
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 بدء تحديث قاعدة البيانات...")
        
        # التحقق من وجود عمود client_id
        cursor.execute("PRAGMA table_info(expenses)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'client_id' not in columns:
            print("➕ إضافة عمود client_id إلى جدول المصروفات...")
            
            # إضافة عمود client_id
            cursor.execute("""
                ALTER TABLE expenses 
                ADD COLUMN client_id INTEGER REFERENCES clients(id)
            """)
            
            # إنشاء فهرس على العمود الجديد
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_expenses_client_id 
                ON expenses(client_id)
            """)
            
            print("✅ تم إضافة عمود client_id بنجاح")
        else:
            print("ℹ️ عمود client_id موجود بالفعل")
        
        # حفظ التغييرات
        conn.commit()
        
        # إظهار إحصائيات
        cursor.execute("SELECT COUNT(*) FROM expenses")
        total_expenses = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM expenses WHERE client_id IS NOT NULL")
        linked_expenses = cursor.fetchone()[0]
        
        print(f"📊 إحصائيات المصروفات:")
        print(f"   - إجمالي المصروفات: {total_expenses}")
        print(f"   - المصروفات المرتبطة بعملاء: {linked_expenses}")
        print(f"   - المصروفات غير المرتبطة: {total_expenses - linked_expenses}")
        
        conn.close()
        print("✅ تم تحديث قاعدة البيانات بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def create_backup():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    db_path = "smart_finish.db"
    
    if not os.path.exists(db_path):
        return False
    
    try:
        backup_name = f"smart_finish_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        
        # نسخ قاعدة البيانات
        import shutil
        shutil.copy2(db_path, backup_name)
        
        print(f"💾 تم إنشاء نسخة احتياطية: {backup_name}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للترحيل"""
    print("🚀 بدء عملية ترحيل قاعدة البيانات...")
    print("=" * 50)
    
    # إنشاء نسخة احتياطية
    print("1️⃣ إنشاء نسخة احتياطية...")
    if create_backup():
        print("✅ تم إنشاء النسخة الاحتياطية بنجاح")
    else:
        print("⚠️ فشل في إنشاء النسخة الاحتياطية، لكن سنتابع...")
    
    print("\n2️⃣ تحديث قاعدة البيانات...")
    if migrate_database():
        print("\n🎉 تم الترحيل بنجاح!")
        print("يمكنك الآن ربط المصروفات بالعملاء وسيتم تحديث أرصدتهم تلقائياً")
    else:
        print("\n❌ فشل في الترحيل")
        return False
    
    print("=" * 50)
    print("✨ انتهت عملية الترحيل")
    return True

if __name__ == "__main__":
    main()
