#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_database():
    """فحص حالة قاعدة البيانات"""
    
    db_path = "smart_finish.db"
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # عرض جميع الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("📋 الجداول الموجودة في قاعدة البيانات:")
        for table in tables:
            print(f"  - {table[0]}")
        
        # التحقق من جدول المصروفات
        if ('expenses',) in tables:
            cursor.execute("SELECT COUNT(*) FROM expenses")
            count = cursor.fetchone()[0]
            print(f"\n💰 جدول المصروفات: {count} سجل")
            
            # عرض هيكل الجدول
            cursor.execute("PRAGMA table_info(expenses)")
            columns = cursor.fetchall()
            print("📊 أعمدة جدول المصروفات:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
        else:
            print("\n❌ جدول المصروفات غير موجود!")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {str(e)}")

if __name__ == "__main__":
    check_database()
